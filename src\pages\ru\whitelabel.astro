---
import Layout from "@layouts/Layout.astro";
import { Image } from "astro:assets";

//#region [i18n]
import { getLangFromUrl, useTranslations } from "@lang/utils";
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
//#endregion [i18n]

// Components
import CTA from "@components/whitelabel/CTA.astro";
import TestsList from "@components/whitelabel/TestsList.astro";

// Images
import HeroImg from "@assets/images/whitelabel/whitelabel-hero.png";
import JuniCoachLogo from "@assets/images/junicoach-logo.svg";

import JunicoachRecordRu from "@assets/images/whitelabel/junicoach-record-ru.png";
import JunicoachResultTestRu from "@assets/images/whitelabel/junicoach-result-test-ru.png";

// How
import ConesImg from "@assets/images/whitelabel/how/cones.jpg";
import PhoneImg from "@assets/images/whitelabel/how/phone.jpg";
import TapeImg from "@assets/images/whitelabel/how/tape.jpg";
import TripodImg from "@assets/images/whitelabel/how/tripod.jpg";
import EquipPhotoImg from "@assets/images/whitelabel/how/equip-photo.jpg";

// Icons
import SolarTShirtBold from "~icons/solar/t-shirt-bold";
import SolarDevicesBoldDuotone from "~icons/solar/devices-bold-duotone";
import SolarCrownMinimalisticBoldDuotone from "~icons/solar/crown-minimalistic-bold-duotone";
import SolarCode2BoldDuotone from "~icons/solar/code-2-bold-duotone";
import SolarVerifiedCheckBold from "~icons/solar/verified-check-bold";
import SolarVideocameraAddBold from "~icons/solar/videocamera-add-bold";
import SolarChartBold from "~icons/solar/chart-bold";
---

<Layout title="Whitelabel-решения для AI-тестирования футболистов" contentOnly>
  <!-- Hero -->
  <section class="">
    <header class="mt-8">
      <div class="container">
        <Image src={JuniCoachLogo} alt="JuniCoach" class="w-auto h-9" />
      </div>
    </header>

    <div class="z-10 relative mt-8 max-lg:mt-10 container">
      <div
        class="flex max-md:flex-col justify-between md:items-center md:gap-4 min-h-[80vh]"
      >
        <div class="flex flex-col items-start max-w-screen-lg lg:max-w-[40rem]">
          <!-- Badge -->
          <div
            class="flex items-center gap-2 bg-bg-accent px-3 py-2 border border-white rounded-full font-medium text-sm"
          >
            <SolarTShirtBold class="size-6" />
            <span>100+ клубов и академий</span>
          </div>

          <!-- Headline -->
          <h1 class="mt-4 font-bold text-3xl md:text-5xl xl:text-6xl">
            Whitelabel-решения для AI-тестирования футболистов
          </h1>
          <p class="mt-4 lg:mt-8 text-base lg:text-xl xl:text-2xl">
            Собственный брендированный раздел с батареей тестов в приложении
            JuniCoach, с логотипом и цветами вашего клуба при заказе
            от 10 000 тестов
          </p>

          <!-- CTA -->
          <CTA class="mt-8 lg:mt-14" />
        </div>

        <Image
          src={HeroImg}
          alt=""
          class="max-md:mt-10 max-w-[30.75rem] xl:max-w-[38.75rem] object-contain"
        />
      </div>
    </div>
  </section>

  <!-- Cards -->
  <section class="mt-14">
    <div class="container">
      <div class="flex flex-col gap-6 lg:grid lg:grid-cols-[1fr_0.7fr_0.7fr]">
        {
          [
            {
              text: "Доступ тренеров и скаутов к результатам в личном кабинете и мобильном приложении",
              icon: SolarDevicesBoldDuotone,
            },
            {
              text: "Тесты по специальной цене при контрактах от 2 млн ₽",
              icon: SolarCrownMinimalisticBoldDuotone,
            },
            {
              text: "Возможность разработки тестов под требования клуба",
              icon: SolarCode2BoldDuotone,
            },
          ].map((card) => {
            return (
              <div class="flex flex-col items-start gap-4 bg-white p-6 rounded-2xl w-full text-lg xl:text-2xl">
                <div class="p-4 rounded-2xl bg-accent-primary">
                  <card.icon class="size-10 lg:size-16" />
                </div>
                <p>{card.text}</p>
              </div>
            );
          })
        }
      </div>
    </div>
  </section>

  <!-- Technology -->
  <section class="mt-28">
    <div class="container">
      <div
        class="flex flex-col gap-6 lg:grid lg:grid-cols-[1fr_0.9fr] lg:grid-rows-auto"
      >
        <!-- Content cell -->
        <div class="items-start max-lg:gap-4 max-lg:grid md:grid-cols-2">
          <div>
            <h2 class="font-bold text-2xl/tight lg:text-5xl">
              Передовая технология тестирования и скаутинга в цветах клуба
            </h2>
            <p class="mt-6 lg:mt-8 text-base lg:text-xl xl:text-2xl">
              Используя только мобильные телефоны и штативы тренеры могут быстро
              тестировать и получать точные данные футболистов, сравнивать
              их с нормативами, определять и развивать таланты
            </p>
          </div>
          <!-- Recommend -->
          <div
            class="flex max-lg:flex-col lg:items-center gap-4 lg:mt-10 p-4 rounded-2xl text-white bg-accent-success"
          >
            <SolarVerifiedCheckBold class="size-16 lg:size-32" />
            <div>
              <div class="font-medium text-lg/tight lg:text-xl xl:text-2xl">
                Проверена специалистами РФС и применяется для комплексной оценки
                игроков
              </div>
              <a href="" class="block mt-2 underline hover:no-underline"
                >Рекомендательное письмо ↗</a
              >
            </div>
          </div>
        </div>

        <!-- Images -->
        <Image
          src={JunicoachResultTestRu}
          alt=""
          class="max-lg:hidden row-span-2 lg:max-w-[32rem]"
        />
        <Image
          src={JunicoachRecordRu}
          alt=""
          class="max-md:w-[150vw] max-w-full max-md:max-w-none"
        />
      </div>
    </div>
  </section>

  <!-- Equipment -->
  <section class="mt-28">
    <div class="container">
      <h2 class="max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
        Тестирование без дорогого оборудования, в любом месте
      </h2>
      <div class="gap-6 grid md:grid-cols-2 mt-12">
        <div
          class="flex max-md:flex-col items-start md:items-center gap-4 bg-white p-6 rounded-2xl w-full text-lg/tight lg:text-2xl"
        >
          <div class="p-4 rounded-2xl bg-accent-primary">
            <SolarVideocameraAddBold class="size-10 lg:size-16" />
          </div>
          <span>Тренер снимает видео по инструкции в приложении</span>
        </div>
        <div
          class="flex max-md:flex-col items-start md:items-center gap-4 bg-white p-6 rounded-2xl w-full text-lg/tight lg:text-2xl"
        >
          <div class="p-4 rounded-2xl bg-accent-primary">
            <SolarChartBold class="size-10 lg:size-16" />
          </div>
          <span
            >Платформа мгновенно обрабатывает данные и выдаёт точные показатели
            игроков
          </span>
        </div>
      </div>
      <div class="gap-6 grid md:grid-cols-2 mt-6">
        <div
          class="max-lg:items-center gap-4 grid grid-cols-[auto_1fr] lg:grid-cols-2 grid-rows-2 max-lg:bg-white max-lg:p-4 max-lg:rounded-2xl"
        >
          {
            [
              {
                title: t("clubs.how.equip_item.phone"),
                img: PhoneImg,
              },
              {
                title: t("clubs.how.equip_item.tripod"),
                img: TripodImg,
              },
              {
                title: t("clubs.how.equip_item.cones"),
                img: ConesImg,
              },
              {
                title: t("clubs.how.equip_item.tape"),
                img: TapeImg,
              },
            ].map((equip) => {
              return (
                <div class="max-lg:contents flex max-lg:flex-col items-center lg:gap-4 lg:bg-white p-6 rounded-2xl w-full text-lg/tight">
                  <Image
                    src={equip.img}
                    alt={equip.title}
                    class="w-26 xl:w-32 h-auto"
                  />
                  <span class="">{equip.title}</span>
                </div>
              );
            })
          }
        </div>
        <CTA class="col-1" />
        <Image
          class="md:row-start-1 rounded-2xl w-full h-full object-cover md:col-2"
          src={EquipPhotoImg}
          alt="Coach tests Antalyaspor team via JuniCoach"
        />
      </div>
    </div>
  </section>

  <!-- Tests -->
  <section class="mt-28">
    <div class="container">
      <h2 class="mb-12 max-w-screen-md font-bold text-2xl/tight lg:text-5xl">
        Тестирование без дорогого оборудования, в любом месте
      </h2>
      <TestsList lang={lang} />
    </div>
  </section>
</Layout>

